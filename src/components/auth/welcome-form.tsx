'use client';

import { FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form } from '@/components/ui/form';
import { FormField } from '../ui/form';
import { FormItem } from '../ui/form';
import { FormControl } from '../ui/form';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { XIcon } from 'lucide-react';
import { useState, useRef, useEffect } from 'react';
import { useUpdateUser } from '@/mutations/user.mutations';
import { useRouter } from 'next/navigation';
import { useGlobalActionsStore } from '@/store/global-actions.store';
import MaleIcon from '../icons/male-icon';
import FemaleIcon from '../icons/female-icon';
import OtherGenderIcon from '../icons/other-gender-icon';
import CalenderIcon from '../icons/calender-icon';

const welcomeFormSchema = z.object({
  fullName: z.string().optional(),
  gender: z.enum(['Male', 'Female', 'Other']).optional(),
  dateOfBirth: z.string().optional(),
  referralCode: z.string().optional(),
});

export type WelcomeFormValues = z.infer<typeof welcomeFormSchema>;

export default function WelcomeForm() {
  const [showReferralCode, setShowReferralCode] = useState(false);
  const dateInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const updateUserMutation = useUpdateUser();
  const { welcomeSheet } = useGlobalActionsStore();
  const form = useForm<WelcomeFormValues>({
    resolver: zodResolver(welcomeFormSchema),
    defaultValues: {
      fullName: undefined,
      gender: undefined,
      dateOfBirth: undefined,
      referralCode: undefined,
    },
    mode: 'onChange',
  });

  const handleContinue = async () => {
    const formData = form.getValues();

    // Convert date from YYYY-MM-DD to DD/MM/YYYY format if provided
    let formattedDateOfBirth = formData.dateOfBirth;
    if (formData.dateOfBirth) {
      const date = new Date(formData.dateOfBirth);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      formattedDateOfBirth = `${day}/${month}/${year}`;
    }

    try {
      await updateUserMutation.mutateAsync({
        name: formData.fullName,
        gender: formData.gender,
        dateOfBirth: formattedDateOfBirth,
        referralCode: formData.referralCode,
      });

      // Navigate to home page after successful submission
      router.push('/');
    } catch (error) {
      console.error('Failed to update user:', error);
      // You might want to show a toast or error message here
    }
  };

  const handleBackToReferralToggle = () => {
    setShowReferralCode(false);
    form.setValue('referralCode', '');
  };

  const handleSubmit = () => {
    form.handleSubmit(handleContinue)();
    welcomeSheet.hide();
  };

  // Handle date picker events to prevent sheet closing
  useEffect(() => {
    const dateInput = dateInputRef.current;
    if (!dateInput) return;

    const handleFocus = () => {
      (window as unknown as { isDatePickerOpen?: boolean }).isDatePickerOpen = true;
    };
    const handleBlur = () => {
      (window as unknown as { isDatePickerOpen?: boolean }).isDatePickerOpen = false;
    };
    const handleChange = () => {
      (window as unknown as { isDatePickerOpen?: boolean }).isDatePickerOpen = false;
    };

    dateInput.addEventListener('focus', handleFocus);
    dateInput.addEventListener('blur', handleBlur);
    dateInput.addEventListener('change', handleChange);

    return () => {
      dateInput.removeEventListener('focus', handleFocus);
      dateInput.removeEventListener('blur', handleBlur);
      dateInput.removeEventListener('change', handleChange);
    };
  }, []);

  return (
    <FormProvider {...form}>
      <Form {...form}>
        <form>
          <div className="flex flex-col h-400 px-16">
            <div className="flex justify-between items-center">
              <p className="font-bold text-lg">WELCOME TO KNOT</p>
            </div>

            <div className="gap-12 flex flex-col pt-8">
              <div className="space-y-6">
                <p className="text-sm font-medium">What should we call you?</p>
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          className="w-full bg-surface-secondary"
                          type="text"
                          placeholder="Type your name"
                          {...field}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              if (!updateUserMutation.isPending) {
                                handleSubmit();
                              }
                            }
                          }}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-6">
                <p className="text-sm font-medium">Gender</p>
                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="flex space-x-12">
                          <Button
                            type="button"
                            variant={field.value === 'Male' ? 'default' : 'secondary'}
                            onClick={() => field.onChange('Male')}
                            className="h-44 w-90 text-sm font-normal rounded-md"
                            data-gender-selected={field.value === 'Male' ? 'true' : 'false'}
                          >
                            <MaleIcon className="size-20 mr-2" /> Male
                          </Button>
                          <Button
                            type="button"
                            variant={field.value === 'Female' ? 'default' : 'secondary'}
                            onClick={() => field.onChange('Female')}
                            className="h-44 w-90 text-sm font-normal rounded-md"
                            data-gender-selected={field.value === 'Female' ? 'true' : 'false'}
                          >
                            <FemaleIcon className="size-20 mr-2" /> Female
                          </Button>
                          <Button
                            type="button"
                            variant={field.value === 'Other' ? 'default' : 'secondary'}
                            onClick={() => field.onChange('Other')}
                            className="h-44 w-90 text-sm font-normal rounded-md"
                            data-gender-selected={field.value === 'Other' ? 'true' : 'false'}
                          >
                            <OtherGenderIcon className="size-20 mr-2" /> Other
                          </Button>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-6">
                <p className="text-sm font-medium">DOB</p>
                <FormField
                  control={form.control}
                  name="dateOfBirth"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="relative">
                          <input
                            ref={dateInputRef}
                            type="date"
                            min="1900-01-01"
                            max="2024-12-31"
                            value={field.value || ''}
                            onChange={field.onChange}
                            className="absolute opacity-0 pointer-events-none"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                if (!updateUserMutation.isPending) {
                                  handleSubmit();
                                }
                              }
                            }}
                          />
                          <button
                            type="button"
                            onClick={() => dateInputRef.current?.showPicker()}
                            className="w-full h-48 px-12 bg-surface-secondary rounded-md flex items-center justify-between transition-colors"
                          >
                            <span className="text-sm text-content-primary">
                              {field.value
                                ? (() => {
                                    const date = new Date(field.value);
                                    const day = date.getDate().toString().padStart(2, '0');
                                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                                    const year = date.getFullYear();
                                    return `${day}/${month}/${year}`;
                                  })()
                                : ''}
                            </span>
                            <CalenderIcon className="size-20 text-content-secondary" />
                          </button>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-6">
                {!showReferralCode ? (
                  <button
                    type="button"
                    onClick={() => setShowReferralCode(true)}
                    className="text-sm font-medium text-left mb-6 text-content-theme"
                  >
                    I have a Referral Code
                  </button>
                ) : (
                  <FormField
                    control={form.control}
                    name="referralCode"
                    render={({ field }) => (
                      <FormItem>
                        <p className="text-sm font-medium mb-4">Enter Your Referral Code</p>
                        <FormControl>
                          <div className="relative">
                            <Input
                              className="w-full bg-surface-secondary pr-32"
                              type="text"
                              placeholder="Enter referral code"
                              {...field}
                              autoFocus
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  if (!updateUserMutation.isPending) {
                                    handleSubmit();
                                  }
                                }
                              }}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={handleBackToReferralToggle}
                              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-neutral-700 rounded-full cursor-pointer"
                            >
                              <XIcon className="size-10" />
                            </Button>
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-col py-12 border-t rounded-md px-16">
            <button
              type="button"
              className="bg-content-theme rounded-sm font-bold w-full disabled:bg-surface-dead-1 transition-colors duration-300 flex items-center justify-center h-48 text-md"
              onClick={handleSubmit}
              disabled={updateUserMutation.isPending}
            >
              Continue
            </button>
          </div>
        </form>
      </Form>
    </FormProvider>
  );
}
