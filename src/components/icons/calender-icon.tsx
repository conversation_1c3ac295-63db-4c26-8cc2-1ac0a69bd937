import { IconProps } from '@/types/icon';

export default function CalenderIcon({ className, fill = '#FAFAFA' }: IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M8.15385 13.9535C8.15385 13.5489 8.49824 13.2209 8.92308 13.2209H15.0769C15.5018 13.2209 15.8462 13.5489 15.8462 13.9535C15.8462 14.3581 15.5018 14.686 15.0769 14.686H8.92308C8.49824 14.686 8.15385 14.3581 8.15385 13.9535Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.92308 1.5C9.34791 1.5 9.69231 1.82798 9.69231 2.23256V3.46385C10.3787 3.45349 11.1414 3.45349 11.9887 3.45349H12.0113C12.8586 3.45349 13.6213 3.45349 14.3077 3.46385V2.23256C14.3077 1.82798 14.6521 1.5 15.0769 1.5C15.5018 1.5 15.8462 1.82798 15.8462 2.23256V3.51559C17.3968 3.60817 18.5882 3.84746 19.569 4.5261C20.0696 4.87251 20.5099 5.29181 20.8737 5.7686C21.6961 6.8466 21.904 8.19155 21.9699 10.0214C22 10.8587 22 11.8329 22 12.966V12.9875C22 14.1206 22 15.0948 21.9699 15.9321C21.904 17.7619 21.6961 19.1069 20.8737 20.1849C20.5099 20.6617 20.0696 21.081 19.569 21.4274C18.437 22.2106 17.0247 22.4086 15.1033 22.4713C14.224 22.5 13.201 22.5 12.0111 22.5H11.9536C10.07 22.5 8.59344 22.5 7.42203 22.3791C6.2266 22.2558 5.25801 21.9996 4.43101 21.4274C3.93035 21.081 3.49006 20.6617 3.12631 20.1849C2.52546 19.3973 2.25642 18.4749 2.1269 17.3365C1.99998 16.2209 1.99999 14.8148 2 13.0209L2 12.9659C2 11.8329 2 10.8587 2.03012 10.0214C2.09595 8.19155 2.30389 6.84659 3.12631 5.7686C3.49006 5.29181 3.93035 4.87251 4.43101 4.5261C5.41185 3.84745 6.60316 3.60817 8.15385 3.51559V2.23256C8.15385 1.82798 8.49824 1.5 8.92308 1.5ZM8.15385 4.98429C6.75687 5.07578 5.95054 5.28572 5.3353 5.71141C4.96524 5.96745 4.63981 6.27736 4.37095 6.62978C3.92395 7.21569 3.70351 7.98358 3.60744 9.31395H20.3926C20.2965 7.98358 20.076 7.21569 19.629 6.62978C19.3602 6.27736 19.0348 5.96745 18.6647 5.71141C18.0495 5.28572 17.2431 5.07578 15.8462 4.98429V5.16279C15.8462 5.56737 15.5018 5.89535 15.0769 5.89535C14.6521 5.89535 14.3077 5.56737 14.3077 5.16279V4.92914C13.6312 4.91865 12.8693 4.9186 12 4.9186C11.1307 4.9186 10.3688 4.91865 9.69231 4.92914V5.16279C9.69231 5.56737 9.34791 5.89535 8.92308 5.89535C8.49824 5.89535 8.15385 5.56737 8.15385 5.16279V4.98429ZM20.4505 10.7791H3.54952C3.53851 11.4233 3.53846 12.1489 3.53846 12.9767C3.53846 14.8243 3.53952 16.1512 3.65641 17.1787C3.77175 18.1924 3.9924 18.8275 4.37095 19.3237C4.63981 19.6761 4.96524 19.986 5.33529 20.2421C5.85632 20.6026 6.52321 20.8127 7.58774 20.9226C8.66666 21.0339 10.0599 21.0349 12 21.0349C12.8701 21.0349 13.6326 21.0348 14.3096 21.0243C14.314 20.5607 14.3292 20.2194 14.3803 19.9121C14.78 17.509 16.7591 15.6242 19.2826 15.2436C19.6052 15.1949 19.9636 15.1805 20.4504 15.1762C20.4615 14.5315 20.4615 13.8054 20.4615 12.9767C20.4615 12.1489 20.4615 11.4233 20.4505 10.7791ZM20.3924 16.6423C19.977 16.6468 19.7335 16.6589 19.5232 16.6906C17.6581 16.972 16.1952 18.3651 15.8998 20.1413C15.8665 20.3416 15.8538 20.5735 15.849 20.969C17.2442 20.8774 18.0499 20.6675 18.6647 20.2421C19.0348 19.986 19.3602 19.6761 19.629 19.3237C20.0757 18.7382 20.2962 17.971 20.3924 16.6423Z"
        fill={fill}
      />
    </svg>
  );
}
