import { IconProps } from '@/types/icon';
export default function FemaleIcon({ className, fill = '#F5F5F5' }: IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M10.2 19.3411H9.00005C8.66005 19.3411 8.37525 19.2327 8.14565 19.0158C7.91605 18.799 7.80085 18.5309 7.80005 18.2117C7.79925 17.8924 7.91445 17.6244 8.14565 17.4075C8.37685 17.1907 8.66165 17.0823 9.00005 17.0823H10.2V14.7105C8.62005 14.447 7.32485 13.7362 6.31445 12.5782C5.30405 11.4201 4.79925 10.0791 4.80005 8.5552C4.80005 6.84226 5.44525 5.38833 6.73565 4.19341C8.02605 2.99849 9.58085 2.40066 11.4 2.3999C13.2192 2.39915 14.7744 2.99699 16.0656 4.19341C17.3568 5.38983 18.0016 6.84376 18 8.5552C18 10.0799 17.4948 11.4213 16.4844 12.5793C15.474 13.7373 14.1792 14.4477 12.6 14.7105V17.0823H13.8C14.14 17.0823 14.4252 17.1907 14.6556 17.4075C14.886 17.6244 15.0008 17.8924 15 18.2117C14.9992 18.5309 14.884 18.7993 14.6544 19.0169C14.4248 19.2345 14.14 19.3426 13.8 19.3411H12.6V20.4705C12.6 20.7905 12.4848 21.0589 12.2544 21.2758C12.024 21.4926 11.7392 21.6007 11.4 21.5999C11.0608 21.5991 10.776 21.4907 10.5456 21.2746C10.3152 21.0585 10.2 20.7905 10.2 20.4705V19.3411ZM11.4 12.5646C12.56 12.5646 13.55 12.1787 14.37 11.407C15.19 10.6352 15.6 9.70343 15.6 8.61167C15.6 7.5199 15.19 6.58814 14.37 5.81637C13.55 5.04461 12.56 4.65873 11.4 4.65873C10.24 4.65873 9.25005 5.04461 8.43005 5.81637C7.61005 6.58814 7.20005 7.5199 7.20005 8.61167C7.20005 9.70343 7.61005 10.6352 8.43005 11.407C9.25005 12.1787 10.24 12.5646 11.4 12.5646Z"
        fill={fill}
      />
    </svg>
  );
}
