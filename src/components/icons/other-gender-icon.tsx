import { IconProps } from '@/types/icon';

export default function OtherGenderIcon({ className, fill = '#F5F5F5' }: IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M12.4422 9.096V7.12579L14.3379 8.2094C14.4553 8.2793 14.5862 8.32619 14.7228 8.34735C14.8595 8.3685 14.9992 8.36349 15.1338 8.3326C15.2684 8.30172 15.3951 8.24558 15.5067 8.16747C15.6183 8.08935 15.7124 7.99083 15.7836 7.87766C15.8548 7.76448 15.9016 7.63892 15.9213 7.5083C15.9411 7.37768 15.9333 7.24463 15.8985 7.11691C15.8636 6.98919 15.8025 6.86936 15.7185 6.76443C15.6346 6.6595 15.5295 6.57156 15.4095 6.50576L13.4252 5.37082L15.4095 4.23588C15.6412 4.09798 15.8068 3.8789 15.8707 3.62578C15.9347 3.37265 15.8918 3.10573 15.7513 2.88244C15.6109 2.65915 15.3841 2.49735 15.1197 2.43185C14.8554 2.36636 14.5746 2.4024 14.3379 2.53223L11.4 4.21187L8.46219 2.53223C8.22545 2.4024 7.9447 2.36636 7.68036 2.43185C7.41602 2.49735 7.18922 2.65915 7.04877 2.88244C6.90831 3.10573 6.86544 3.37265 6.92936 3.62578C6.99328 3.8789 7.15889 4.09798 7.39056 4.23588L9.37489 5.37082L7.39056 6.50576C7.27056 6.57156 7.16552 6.6595 7.08158 6.76443C6.99763 6.86936 6.93646 6.98919 6.90164 7.11691C6.86681 7.24463 6.85903 7.37768 6.87875 7.5083C6.89846 7.63892 6.94528 7.76448 7.01647 7.87766C7.08766 7.99083 7.1818 8.08935 7.29338 8.16747C7.40495 8.24558 7.53174 8.30172 7.66633 8.3326C7.80092 8.36349 7.94062 8.3685 8.07726 8.34735C8.21391 8.32619 8.34476 8.2793 8.46219 8.2094L10.3579 7.12579V9.096C8.71882 9.34584 7.23865 10.1753 6.21244 11.419C5.18622 12.6627 4.68933 14.2294 4.82079 15.8067C4.95225 17.3841 5.70241 18.8563 6.92176 19.93C8.14111 21.0037 9.7401 21.5999 11.4 21.5999C13.06 21.5999 14.659 21.0037 15.8783 19.93C17.0977 18.8563 17.8478 17.3841 17.9793 15.8067C18.1108 14.2294 17.6139 12.6627 16.5877 11.419C15.5614 10.1753 14.0813 9.34584 12.4422 9.096ZM11.4 19.6093C10.5069 19.6093 9.63383 19.3568 8.89122 18.8838C8.1486 18.4108 7.5698 17.7385 7.22801 16.9519C6.88622 16.1654 6.79679 15.2999 6.97104 14.4648C7.14528 13.6298 7.57537 12.8628 8.20691 12.2608C8.83845 11.6588 9.64309 11.2488 10.5191 11.0827C11.395 10.9166 12.303 11.0018 13.1282 11.3277C13.9533 11.6535 14.6586 12.2052 15.1548 12.9131C15.651 13.621 15.9158 14.4532 15.9158 15.3046C15.9144 16.4459 15.4382 17.54 14.5917 18.347C13.7451 19.154 12.5973 19.608 11.4 19.6093Z"
        fill={fill}
      />
    </svg>
  );
}
